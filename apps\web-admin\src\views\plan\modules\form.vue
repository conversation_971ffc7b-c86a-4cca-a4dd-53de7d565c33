<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createPlan, updatePlan } from '#/api/plan';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    // 处理上传组件的数据格式
    const submitData = { ...values };
    if (submitData.mainImage) {
      submitData.mainImage = formatSubmitValue(submitData.mainImage);
    }

    // 处理计划配置数据
    if (submitData.planConfig) {
      // 将planConfig展开到根级别
      const { planConfig, ...otherData } = submitData;
      Object.assign(submitData, otherData, planConfig);
      delete submitData.planConfig;
    }

    drawerApi.lock();
    try {
      if (id.value) {
        await updatePlan(id.value, submitData);
        message.success(`${$t('plan.name')}修改成功`);
      } else {
        await createPlan(submitData);
        message.success(`${$t('plan.name')}创建成功`);
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;

        // 处理上传组件的数据格式
        const formValues = { ...data };
        if (formValues.mainImage) {
          formValues.mainImage = formatUploadValue(formValues.mainImage);
        }

        // 处理计划配置数据回显
        if (data.promotion_content || data.placement_rules || data.audience_criteria) {
          formValues.planConfig = {
            promotion_content: data.promotion_content || {},
            placement_rules: data.placement_rules || {},
            audience_criteria: data.audience_criteria || {},
          };
        }

        formApi.setValues(formValues);
      } else {
        formData.value = {};
        id.value = undefined;
      }
    }
  },
});

// 处理上传组件提交的数据格式
function formatSubmitValue(fileList: any[]) {
  if (!fileList || !Array.isArray(fileList) || fileList.length === 0) {
    return '';
  }
  const file = fileList[0];
  return file.response?.url || file.url || '';
}

// 处理上传组件的数据格式
function formatUploadValue(url: string) {
  if (!url) return [];
  return [
    {
      uid: '-1',
      name: 'image.png',
      status: 'done',
      url,
    },
  ];
}

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `修改${$t('plan.name')}`
    : `新增${$t('plan.name')}`;
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
