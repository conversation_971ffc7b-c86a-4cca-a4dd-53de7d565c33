<template>
  <Select
    v-model:value="selectedValue"
    :loading="isLoading"
    :placeholder="placeholder"
    :allow-clear="allowClear"
    show-search
    :filter-option="false"
    :not-found-content="isLoading ? '加载中...' : '暂无数据'"
    style="width: 100%; min-width: 200px;"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear"
  >
    <SelectOption
      v-for="product in filteredProducts"
      :key="product.value"
      :value="product.value"
      :label="product.label"
    >
      <div class="flex items-center space-x-2">
        <img
          v-if="product.logo"
          :src="product.logo"
          :alt="product.label"
          class="w-6 h-6 rounded object-cover flex-shrink-0"
        />
        <div v-else class="w-6 h-6 bg-gray-200 rounded flex items-center justify-center flex-shrink-0">
          <span class="text-xs text-gray-500">无</span>
        </div>
        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium truncate">{{ product.label }}</div>
          <div v-if="product.category || product.company" class="text-xs text-gray-500 truncate">
            {{ [product.category, product.company].filter(Boolean).join(' · ') }}
          </div>
        </div>
      </div>
    </SelectOption>
  </Select>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Select, SelectOption } from 'ant-design-vue';
import { useProductData, type ProductOption } from '../composables/useProductData';

interface Props {
  value?: number | string;
  placeholder?: string;
  allowClear?: boolean;
}

interface Emits {
  (e: 'update:value', value: number | string | undefined): void;
  (e: 'change', value: number | string | undefined, option?: ProductOption): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择产品',
  allowClear: true,
});

const emits = defineEmits<Emits>();

// 使用产品数据管理
const { products, isLoading, preloadProducts, searchProducts } = useProductData();

// 内部状态
const selectedValue = ref<number | string | undefined>(props.value);
const searchKeyword = ref('');

// 过滤后的产品列表
const filteredProducts = computed(() => {
  if (!searchKeyword.value) {
    return products.value;
  }
  return searchProducts(searchKeyword.value);
});

// 处理搜索
function handleSearch(value: string) {
  console.log('🔍 ProductSelector 搜索:', value);
  searchKeyword.value = value;
  console.log('📋 ProductSelector 过滤后产品数量:', filteredProducts.value.length);
}

// 处理选择变化
function handleChange(value: number | string | undefined) {
  console.log('✅ ProductSelector 选择变化:', value);
  selectedValue.value = value;
  emits('update:value', value);

  // 转换为数字进行查找
  const numValue = typeof value === 'string' ? parseInt(value, 10) : value;
  const selectedProduct = products.value.find(p => p.value === numValue);
  console.log('📦 ProductSelector 选中的产品:', selectedProduct);
  emits('change', value, selectedProduct);
}

// 处理清除
function handleClear() {
  console.log('🗑️ ProductSelector 清除选择');
  selectedValue.value = undefined;
  searchKeyword.value = '';
  emits('update:value', undefined);
  emits('change', undefined);
}

// 监听外部值变化
watch(
  () => props.value,
  (newValue) => {
    selectedValue.value = newValue;
  },
  { immediate: true }
);

// 组件挂载时预加载产品数据
onMounted(async () => {
  try {
    console.log('🔄 ProductSelector 组件挂载，开始预加载产品数据...');
    await preloadProducts();
    console.log('✅ ProductSelector 产品数据预加载完成，产品数量:', products.value.length);
  } catch (error) {
    console.error('❌ ProductSelector 预加载产品数据失败:', error);
  }
});
</script>

<style scoped>
/* 产品选择器样式 */
:deep(.ant-select-dropdown) {
  max-height: 300px;
}

:deep(.ant-select-item-option-content) {
  padding: 0;
}

:deep(.ant-select-item) {
  padding: 8px 12px;
}

:deep(.ant-select-item:hover) {
  background-color: var(--ant-primary-color-hover);
}
</style>
