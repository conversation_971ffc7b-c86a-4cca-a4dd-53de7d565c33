<template>
  <div class="plan-config-editor">
    <!-- 投放内容配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放内容</h4>
        <Button
          type="primary"
          size="small"
          @click="addPromotionItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加投放内容
        </Button>
      </div>

      <PromotionGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePromotionItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放内容</div>
          </div>
        </template>
      </PromotionGrid>
    </div>

    <!-- 投放规则配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放规则</h4>
        <Button
          type="primary"
          size="small"
          @click="addPlacementItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加投放规则
        </Button>
      </div>

      <PlacementGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePlacementItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放规则</div>
          </div>
        </template>
      </PlacementGrid>
    </div>

    <!-- 用户定向条件配置 -->
    <div class="config-section">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">用户定向条件</h4>
        <Button
          type="primary"
          size="small"
          @click="addAudienceItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加定向条件
        </Button>
      </div>

      <AudienceGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removeAudienceItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无定向条件</div>
          </div>
        </template>
      </AudienceGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted } from 'vue';
import { Button } from 'ant-design-vue';
import { Plus, EmptyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';

interface PlanConfigItem {
  id: string;
  title: string;
  content: string;
}

interface BackendConfigItem {
  title: string;
  content: string;
}

interface PlanConfig {
  promotion_content?: BackendConfigItem[];
  placement_rules?: BackendConfigItem[];
  audience_criteria?: BackendConfigItem[];
}

interface Props {
  modelValue?: any; // 接受完整的计划数据
}

interface Emits {
  (e: 'update:modelValue', value: any): void;
  (e: 'change', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const promotionItems = ref<PlanConfigItem[]>([]);
const placementItems = ref<PlanConfigItem[]>([]);
const audienceItems = ref<PlanConfigItem[]>([]);

// 组件挂载状态
const isMounted = ref(false);



// 创建基础表格配置函数
function createGridOptions(): VxeGridProps<PlanConfigItem> {
  return {
    columns: [
      {
        field: 'title',
        title: '标题',
        width: '40%',
        minWidth: 120,
        editRender: { name: 'input' },
      },
      {
        field: 'content',
        title: '内容',
        width: '45%',
        minWidth: 180,
        editRender: { name: 'input' },
      },
      {
        title: '操作',
        width: '15%',
        minWidth: 80,
        slots: { default: 'action' },
        align: 'center',
      },
    ],
    data: [],
    editConfig: {
      mode: 'cell',
      trigger: 'click',
    },
    height: 'auto',
    pagerConfig: { enabled: false },
    toolbarConfig: { enabled: false },
    showOverflow: false,
    border: true,
    autoResize: true,
    scrollY: { enabled: false },
    scrollX: { enabled: false },
  };
}

// 创建表格事件处理函数
function createGridEvents(): VxeGridListeners<PlanConfigItem> {
  return {
    editClosed: () => {
      handleChange();
    },
  };
}

// 创建投放内容表格实例
const [PromotionGrid, promotionGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

// 创建投放规则表格实例
const [PlacementGrid, placementGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

// 创建用户定向条件表格实例
const [AudienceGrid, audienceGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

// 统一的表格数据更新函数
function updateGridData() {
  console.log('🔄 更新表格数据...');
  console.log('📊 当前数据状态:', {
    promotion: promotionItems.value.length,
    placement: placementItems.value.length,
    audience: audienceItems.value.length,
    isMounted: isMounted.value
  });

  if (!isMounted.value) {
    console.log('⏳ 组件未挂载，延迟更新表格');
    // 如果组件未挂载，等待挂载后再更新
    return;
  }

  nextTick(() => {
    try {
      // 检查API是否可用
      if (!promotionGridApi || !placementGridApi || !audienceGridApi) {
        console.warn('⚠️ 表格API未初始化，跳过更新');
        return;
      }

      // 更新投放内容表格
      promotionGridApi.setGridOptions({ data: [...promotionItems.value] });
      console.log('✅ 投放内容表格数据已更新，数据量:', promotionItems.value.length);

      // 更新投放规则表格
      placementGridApi.setGridOptions({ data: [...placementItems.value] });
      console.log('✅ 投放规则表格数据已更新，数据量:', placementItems.value.length);

      // 更新用户定向条件表格
      audienceGridApi.setGridOptions({ data: [...audienceItems.value] });
      console.log('✅ 用户定向条件表格数据已更新，数据量:', audienceItems.value.length);
    } catch (error) {
      console.error('❌ 更新表格数据失败:', error);
    }
  });
}

// 初始化数据
function initializeItems() {
  const rawData = props.modelValue || {};
  console.log('🔄 PlanConfigEditor 接收到的原始数据:', rawData);
  console.log('🔍 PlanConfigEditor 数据类型:', typeof rawData);

  // 检查数据是否在configData字段中，还是直接在根级别
  let config = rawData;
  if (rawData.configData && typeof rawData.configData === 'object') {
    config = rawData.configData;
    console.log('� 从configData字段中提取配置数据:', config);
  } else if (rawData.promotion_content || rawData.placement_rules || rawData.audience_criteria) {
    config = rawData;
    console.log('📦 直接使用根级别配置数据:', config);
  } else {
    config = {};
    console.log('📦 未找到配置数据，使用空对象');
  }

  console.log('🔍 最终使用的配置数据:', config);
  console.log('🔍 promotion_content:', config.promotion_content);
  console.log('🔍 placement_rules:', config.placement_rules);
  console.log('🔍 audience_criteria:', config.audience_criteria);

  // 初始化投放内容 - 处理数组格式
  const promotionContent = config.promotion_content || [];
  console.log('🔄 处理投放内容数据:', promotionContent, '类型:', Array.isArray(promotionContent));
  promotionItems.value = promotionContent.map((item: BackendConfigItem, index: number) => ({
    id: `promotion_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化投放规则 - 处理数组格式
  const placementRules = config.placement_rules || [];
  console.log('🔄 处理投放规则数据:', placementRules, '类型:', Array.isArray(placementRules));
  placementItems.value = placementRules.map((item: BackendConfigItem, index: number) => ({
    id: `placement_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化用户定向条件 - 处理数组格式
  const audienceCriteria = config.audience_criteria || [];
  console.log('🔄 处理用户定向条件数据:', audienceCriteria, '类型:', Array.isArray(audienceCriteria));
  audienceItems.value = audienceCriteria.map((item: BackendConfigItem, index: number) => ({
    id: `audience_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 如果没有任何数据，添加一些测试数据来验证回显功能
  if (promotionItems.value.length === 0 && placementItems.value.length === 0 && audienceItems.value.length === 0) {
    console.log('🧪 没有配置数据，添加测试数据验证回显功能');
    promotionItems.value = [
      { id: 'test_promotion_1', title: '测试投放内容1', content: '这是测试投放内容1的描述' },
      { id: 'test_promotion_2', title: '测试投放内容2', content: '这是测试投放内容2的描述' },
    ];
    placementItems.value = [
      { id: 'test_placement_1', title: '测试投放规则1', content: '这是测试投放规则1的描述' },
    ];
    audienceItems.value = [
      { id: 'test_audience_1', title: '测试定向条件1', content: '这是测试定向条件1的描述' },
      { id: 'test_audience_2', title: '测试定向条件2', content: '这是测试定向条件2的描述' },
      { id: 'test_audience_3', title: '测试定向条件3', content: '这是测试定向条件3的描述' },
    ];
  }

  console.log('📊 数据初始化完成');

  console.log('📋 初始化后的数据:', {
    promotion: promotionItems.value,
    placement: placementItems.value,
    audience: audienceItems.value,
  });

  // 更新表格数据
  updateGridData();
}

// 添加投放内容项
function addPromotionItem() {
  console.log('🔄 添加投放内容项');
  const newItem = {
    id: `promotion_${Date.now()}_${promotionItems.value.length}`,
    title: '',
    content: '',
  };

  promotionItems.value.push(newItem);
  console.log('🔄 添加投放内容项完成，当前数量111111:', promotionItems.value);
  // 确保组件已挂载且API可用
  if (isMounted.value && promotionGridApi) {
    nextTick(() => {
      try {
        promotionGridApi.setGridOptions({ data: [...promotionItems.value] });
        console.log('✅ 添加投放内容项完成，当前数量:', promotionItems.value.length);
      } catch (error) {
        console.error('❌ 更新投放内容表格失败:', error);
      }
    });
  }

  handleChange();
}

// 删除投放内容项
function removePromotionItem(index: number) {
  console.log('🗑️ 删除投放内容项:', index);
  if (index >= 0 && index < promotionItems.value.length) {
    const removedItem = promotionItems.value.splice(index, 1)[0];
    console.log('✅ 已删除投放内容项:', removedItem);

    // 确保组件已挂载且API可用
    if (isMounted.value && promotionGridApi) {
      nextTick(() => {
        try {
          promotionGridApi.setGridOptions({ data: [...promotionItems.value] });
        } catch (error) {
          console.error('❌ 更新投放内容表格失败:', error);
        }
      });
    }

    handleChange();
  }
}

// 添加投放规则项
function addPlacementItem() {
  console.log('🔄 添加投放规则项');
  const newItem = {
    id: `placement_${Date.now()}_${placementItems.value.length}`,
    title: '',
    content: '',
  };
  placementItems.value.push(newItem);
  console.log('✅ 新增投放规则项:', newItem);

  // 使用nextTick确保数据更新后再更新表格
  nextTick(() => {
    placementGridApi.setGridOptions({ data: [...placementItems.value] });
  });

  handleChange();
}

// 删除投放规则项
function removePlacementItem(index: number) {
  console.log('🗑️ 删除投放规则项:', index);
  if (index >= 0 && index < placementItems.value.length) {
    const removedItem = placementItems.value.splice(index, 1)[0];
    console.log('✅ 已删除投放规则项:', removedItem);

    // 使用nextTick确保数据更新后再更新表格
    nextTick(() => {
      placementGridApi.setGridOptions({ data: [...placementItems.value] });
    });

    handleChange();
  }
}

// 添加用户定向条件项
function addAudienceItem() {
  console.log('🔄 添加用户定向条件项');
  const newItem = {
    id: `audience_${Date.now()}_${audienceItems.value.length}`,
    title: '',
    content: '',
  };
  audienceItems.value.push(newItem);
  console.log('✅ 新增用户定向条件项:', newItem);

  // 使用nextTick确保数据更新后再更新表格
  nextTick(() => {
    audienceGridApi.setGridOptions({ data: [...audienceItems.value] });
  });

  handleChange();
}

// 删除用户定向条件项
function removeAudienceItem(index: number) {
  console.log('🗑️ 删除用户定向条件项:', index);
  if (index >= 0 && index < audienceItems.value.length) {
    const removedItem = audienceItems.value.splice(index, 1)[0];
    console.log('✅ 已删除用户定向条件项:', removedItem);

    // 使用nextTick确保数据更新后再更新表格
    nextTick(() => {
      audienceGridApi.setGridOptions({ data: [...audienceItems.value] });
    });

    handleChange();
  }
}

// 处理变更
function handleChange() {
  try {
    // 获取当前的完整数据
    const result = { ...props.modelValue };

    // 处理投放内容 - 转换为数组格式
    const promotionContent: BackendConfigItem[] = promotionItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.promotion_content = promotionContent;

    // 处理投放规则 - 转换为数组格式
    const placementRules: BackendConfigItem[] = placementItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.placement_rules = placementRules;

    // 处理用户定向条件 - 转换为数组格式
    const audienceCriteria: BackendConfigItem[] = audienceItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.audience_criteria = audienceCriteria;

    console.log('🔄 PlanConfigEditor 数据变更:', result);
    emits('update:modelValue', result);
    emits('change', result);
  } catch (error) {
    console.error('❌ PlanConfigEditor 处理变更失败:', error);
  }
}

// 组件挂载后设置状态
onMounted(() => {
  console.log('🚀 PlanConfigEditor 组件已挂载');
  isMounted.value = true;

  // 挂载后重新初始化数据
  initializeItems();

  // 确保表格数据更新
  nextTick(() => {
    console.log('🔄 挂载后强制更新表格数据');
    updateGridData();
  });
});

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('👀 PlanConfigEditor 监听到数据变化:', newValue);
    if (isMounted.value) {
      initializeItems();
    }
  },
  { immediate: false, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的title
    const allTitles = [
      ...promotionItems.value.map(item => `promotion_${item.title?.trim()}`),
      ...placementItems.value.map(item => `placement_${item.title?.trim()}`),
      ...audienceItems.value.map(item => `audience_${item.title?.trim()}`),
    ].filter(title => title && title !== 'promotion_' && title !== 'placement_' && title !== 'audience_');

    const uniqueTitles = new Set(allTitles);
    if (allTitles.length !== uniqueTitles.size) {
      return {
        valid: false,
        message: '配置项标题不能重复',
      };
    }

    return { valid: true };
  },
});
</script>

<style scoped>
.plan-config-editor {
  @apply w-full;
}

.config-section {
  @apply border border-border rounded-lg p-4;
}

.config-section h4 {
  @apply text-foreground;
}

/* 确保删除按钮样式与系统一致 */
.plan-config-editor :deep(.ant-btn-link) {
  color: hsl(var(--destructive));
  padding: 0;
  height: auto;
  line-height: 1.5;
}

.plan-config-editor :deep(.ant-btn-link:hover) {
  color: hsl(var(--destructive) / 80%);
}

/* VXE表格布局优化 */
.plan-config-editor :deep(.vxe-table) {
  width: 100% !important;
  height: auto !important;
  table-layout: fixed;
}

.plan-config-editor :deep(.vxe-table--main-wrapper) {
  overflow: visible !important;
  height: auto !important;
}

.plan-config-editor :deep(.vxe-table--body-wrapper) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

.plan-config-editor :deep(.vxe-table--header-wrapper) {
  overflow: visible !important;
}

/* 确保表格容器高度自适应 */
.plan-config-editor :deep(.vxe-grid) {
  height: auto !important;
}

.plan-config-editor :deep(.vxe-grid--wrapper) {
  height: auto !important;
}

/* 单元格内容优化 */
.plan-config-editor :deep(.vxe-cell) {
  padding: 8px 12px;
  word-wrap: break-word;
  word-break: break-all;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input) {
  width: 100%;
  border: none;
  box-shadow: none;
  background: transparent;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input:focus) {
  border: 1px solid hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 20%);
  background: hsl(var(--background));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 6px 8px;
    font-size: 14px;
  }

  .plan-config-editor :deep(.ant-btn-link) {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 4px 6px;
    font-size: 13px;
  }
}
</style>
