<template>
  <div class="plan-config-editor">
    <!-- 投放内容配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放内容</h4>
        <Button
          type="primary"
          size="small"
          @click="addPromotionItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加投放内容
        </Button>
      </div>

      <PromotionGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePromotionItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放内容</div>
          </div>
        </template>
      </PromotionGrid>
    </div>

    <!-- 投放规则配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放规则</h4>
        <Button
          type="primary"
          size="small"
          @click="addPlacementItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加投放规则
        </Button>
      </div>

      <PlacementGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePlacementItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放规则</div>
          </div>
        </template>
      </PlacementGrid>
    </div>

    <!-- 用户定向条件配置 -->
    <div class="config-section">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">用户定向条件</h4>
        <Button
          type="primary"
          size="small"
          @click="addAudienceItem"
        >
          <Plus class="w-4 h-4 mr-1" />
          添加定向条件
        </Button>
      </div>

      <AudienceGrid>
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removeAudienceItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无定向条件</div>
          </div>
        </template>
      </AudienceGrid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Button } from 'ant-design-vue';
import { Plus, EmptyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';

interface PlanConfigItem {
  id: string;
  title: string;
  content: string;
}

interface BackendConfigItem {
  title: string;
  content: string;
}

interface PlanConfig {
  promotion_content?: BackendConfigItem[];
  placement_rules?: BackendConfigItem[];
  audience_criteria?: BackendConfigItem[];
}

interface Props {
  modelValue?: any; // 接受完整的计划数据
}

interface Emits {
  (e: 'update:modelValue', value: any): void;
  (e: 'change', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const promotionItems = ref<PlanConfigItem[]>([]);
const placementItems = ref<PlanConfigItem[]>([]);
const audienceItems = ref<PlanConfigItem[]>([]);



// 投放内容表格配置
const promotionGridOptions: VxeGridProps<PlanConfigItem> = {
  columns: [
    {
      field: 'title',
      title: '标题',
      width: '40%',
      minWidth: 120,
      editRender: { name: 'input' },
    },
    {
      field: 'content',
      title: '内容',
      width: '45%',
      minWidth: 180,
      editRender: { name: 'input' },
    },
    {
      title: '操作',
      width: '15%',
      minWidth: 80,
      slots: { default: 'action' },
      align: 'center',
    },
  ],
  data: [],
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: { enabled: false },
  toolbarConfig: { enabled: false },
  showOverflow: false,
  border: true,
  autoResize: true,
  scrollY: { enabled: false },
  scrollX: { enabled: false },
};

// 投放内容表格事件
const promotionGridEvents: VxeGridListeners<PlanConfigItem> = {
  editClosed: () => {
    handleChange();
  },
};

// 创建投放内容表格实例
const [PromotionGrid, promotionGridApi] = useVbenVxeGrid({
  gridOptions: {
    ...promotionGridOptions,
    data: promotionItems.value,
  },
  gridEvents: promotionGridEvents,
});

// 创建投放规则表格实例
const [PlacementGrid, placementGridApi] = useVbenVxeGrid({
  gridOptions: {
    ...promotionGridOptions,
    data: placementItems.value,
  },
  gridEvents: {
    editClosed: () => {
      handleChange();
    },
  },
});

// 创建用户定向条件表格实例
const [AudienceGrid, audienceGridApi] = useVbenVxeGrid({
  gridOptions: {
    ...promotionGridOptions,
    data: audienceItems.value,
  },
  gridEvents: {
    editClosed: () => {
      handleChange();
    },
  },
});

// 初始化数据
function initializeItems() {
  const config = props.modelValue || {};
  console.log('🔄 PlanConfigEditor 初始化数据:', config);
  console.log('🔍 PlanConfigEditor 数据类型:', typeof config);
  console.log('🔍 PlanConfigEditor promotion_content:', config.promotion_content);
  console.log('🔍 PlanConfigEditor placement_rules:', config.placement_rules);
  console.log('🔍 PlanConfigEditor audience_criteria:', config.audience_criteria);

  // 初始化投放内容 - 处理数组格式
  promotionItems.value = (config.promotion_content || []).map((item: BackendConfigItem, index: number) => ({
    id: `promotion_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化投放规则 - 处理数组格式
  placementItems.value = (config.placement_rules || []).map((item: BackendConfigItem, index: number) => ({
    id: `placement_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化用户定向条件 - 处理数组格式
  audienceItems.value = (config.audience_criteria || []).map((item: BackendConfigItem, index: number) => ({
    id: `audience_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 如果没有数据，添加一些测试数据来验证表格是否工作
  if (promotionItems.value.length === 0 && placementItems.value.length === 0 && audienceItems.value.length === 0) {
    console.log('🧪 添加测试数据...');
    promotionItems.value = [
      { id: 'test_1', title: '测试标题1', content: '测试内容1' },
    ];
    placementItems.value = [
      { id: 'test_2', title: '测试标题2', content: '测试内容2' },
    ];
    audienceItems.value = [
      { id: 'test_3', title: '测试标题3', content: '测试内容3' },
    ];
  }

  console.log('📋 初始化后的数据:', {
    promotion: promotionItems.value,
    placement: placementItems.value,
    audience: audienceItems.value,
  });

  // 更新表格数据
  console.log('🔄 更新表格数据...');
  try {
    promotionGridApi.setGridOptions({ data: promotionItems.value });
    console.log('✅ 投放内容表格数据已更新，数据量:', promotionItems.value.length);

    placementGridApi.setGridOptions({ data: placementItems.value });
    console.log('✅ 投放规则表格数据已更新，数据量:', placementItems.value.length);

    audienceGridApi.setGridOptions({ data: audienceItems.value });
    console.log('✅ 用户定向条件表格数据已更新，数据量:', audienceItems.value.length);
  } catch (error) {
    console.error('❌ 更新表格数据失败:', error);
  }
}

// 添加投放内容项
function addPromotionItem() {
  console.log('🔄 添加投放内容项');
  const newItem = {
    id: `promotion_${Date.now()}_${promotionItems.value.length}`,
    title: '',
    content: '',
  };

  promotionItems.value.push(newItem);
  promotionGridApi.setGridOptions({ data: promotionItems.value });
  handleChange();
  console.log('✅ 添加投放内容项完成，当前数量:', promotionItems.value.length);
}

// 删除投放内容项
function removePromotionItem(index: number) {
  if (index >= 0 && index < promotionItems.value.length) {
    promotionItems.value.splice(index, 1);
    promotionGridApi.setGridOptions({ data: promotionItems.value });
    handleChange();
  }
}

// 添加投放规则项
function addPlacementItem() {
  console.log('🔄 添加投放规则项');
  try {
    const newItem = {
      id: `placement_${Date.now()}_${placementItems.value.length}`,
      title: '',
      content: '',
    };
    placementItems.value.push(newItem);
    console.log('✅ 新增投放规则项:', newItem);

    placementGridApi.setGridOptions({ data: placementItems.value });
    handleChange();
  } catch (error) {
    console.error('❌ 添加投放规则项失败:', error);
  }
}

// 删除投放规则项
function removePlacementItem(index: number) {
  console.log('🗑️ 删除投放规则项:', index);
  if (index >= 0 && index < placementItems.value.length) {
    const removedItem = placementItems.value.splice(index, 1)[0];
    console.log('✅ 已删除投放规则项:', removedItem);

    placementGridApi.setGridOptions({ data: placementItems.value });
    handleChange();
  }
}

// 添加用户定向条件项
function addAudienceItem() {
  console.log('🔄 添加用户定向条件项');
  try {
    const newItem = {
      id: `audience_${Date.now()}_${audienceItems.value.length}`,
      title: '',
      content: '',
    };
    audienceItems.value.push(newItem);
    console.log('✅ 新增用户定向条件项:', newItem);

    audienceGridApi.setGridOptions({ data: audienceItems.value });
    handleChange();
  } catch (error) {
    console.error('❌ 添加用户定向条件项失败:', error);
  }
}

// 删除用户定向条件项
function removeAudienceItem(index: number) {
  if (index >= 0 && index < audienceItems.value.length) {
    audienceItems.value.splice(index, 1);
    audienceGridApi.setGridOptions({ data: audienceItems.value });
    handleChange();
  }
}

// 处理变更
function handleChange() {
  try {
    // 获取当前的完整数据
    const result = { ...props.modelValue };

    // 处理投放内容 - 转换为数组格式
    const promotionContent: BackendConfigItem[] = promotionItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.promotion_content = promotionContent;

    // 处理投放规则 - 转换为数组格式
    const placementRules: BackendConfigItem[] = placementItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.placement_rules = placementRules;

    // 处理用户定向条件 - 转换为数组格式
    const audienceCriteria: BackendConfigItem[] = audienceItems.value
      .filter(item => item.title && item.title.trim())
      .map(item => ({
        title: item.title.trim(),
        content: item.content || '',
      }));
    result.audience_criteria = audienceCriteria;

    console.log('🔄 PlanConfigEditor 数据变更:', result);
    emits('update:modelValue', result);
    emits('change', result);
  } catch (error) {
    console.error('❌ PlanConfigEditor 处理变更失败:', error);
  }
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    console.log('👀 PlanConfigEditor 监听到数据变化:', newValue);
    initializeItems();
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的title
    const allTitles = [
      ...promotionItems.value.map(item => `promotion_${item.title?.trim()}`),
      ...placementItems.value.map(item => `placement_${item.title?.trim()}`),
      ...audienceItems.value.map(item => `audience_${item.title?.trim()}`),
    ].filter(title => title && title !== 'promotion_' && title !== 'placement_' && title !== 'audience_');

    const uniqueTitles = new Set(allTitles);
    if (allTitles.length !== uniqueTitles.size) {
      return {
        valid: false,
        message: '配置项标题不能重复',
      };
    }

    return { valid: true };
  },
});
</script>

<style scoped>
.plan-config-editor {
  @apply w-full;
}

.config-section {
  @apply border border-border rounded-lg p-4;
}

.config-section h4 {
  @apply text-foreground;
}

/* 确保删除按钮样式与系统一致 */
.plan-config-editor :deep(.ant-btn-link) {
  color: hsl(var(--destructive));
  padding: 0;
  height: auto;
  line-height: 1.5;
}

.plan-config-editor :deep(.ant-btn-link:hover) {
  color: hsl(var(--destructive) / 80%);
}

/* VXE表格布局优化 */
.plan-config-editor :deep(.vxe-table) {
  width: 100% !important;
  height: auto !important;
  table-layout: fixed;
}

.plan-config-editor :deep(.vxe-table--main-wrapper) {
  overflow: visible !important;
  height: auto !important;
}

.plan-config-editor :deep(.vxe-table--body-wrapper) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

.plan-config-editor :deep(.vxe-table--header-wrapper) {
  overflow: visible !important;
}

/* 确保表格容器高度自适应 */
.plan-config-editor :deep(.vxe-grid) {
  height: auto !important;
}

.plan-config-editor :deep(.vxe-grid--wrapper) {
  height: auto !important;
}

/* 单元格内容优化 */
.plan-config-editor :deep(.vxe-cell) {
  padding: 8px 12px;
  word-wrap: break-word;
  word-break: break-all;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input) {
  width: 100%;
  border: none;
  box-shadow: none;
  background: transparent;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input:focus) {
  border: 1px solid hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 20%);
  background: hsl(var(--background));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 6px 8px;
    font-size: 14px;
  }

  .plan-config-editor :deep(.ant-btn-link) {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 4px 6px;
    font-size: 13px;
  }
}
</style>
