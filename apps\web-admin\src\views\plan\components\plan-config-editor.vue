<template>
  <div class="plan-config-editor">
    <!-- 投放内容配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放内容</h4>
        <Button type="primary" size="small" @click="addPromotionItem">
          <Plus class="w-4 h-4 mr-1" />
          添加投放内容
        </Button>
      </div>
      
      <Grid ref="promotionGridRef">
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePromotionItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放内容</div>
          </div>
        </template>
      </Grid>
    </div>

    <!-- 投放规则配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放规则</h4>
        <Button type="primary" size="small" @click="addPlacementItem">
          <Plus class="w-4 h-4 mr-1" />
          添加投放规则
        </Button>
      </div>
      
      <Grid ref="placementGridRef">
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removePlacementItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无投放规则</div>
          </div>
        </template>
      </Grid>
    </div>

    <!-- 用户定向条件配置 -->
    <div class="config-section">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">用户定向条件</h4>
        <Button type="primary" size="small" @click="addAudienceItem">
          <Plus class="w-4 h-4 mr-1" />
          添加定向条件
        </Button>
      </div>
      
      <Grid ref="audienceGridRef">
        <template #action="{ row, rowIndex }">
          <Button
            type="link"
            danger
            size="small"
            @click="removeAudienceItem(rowIndex)"
          >
            删除
          </Button>
        </template>
        <template #empty>
          <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
            <EmptyIcon class="size-10 mb-2" />
            <div class="text-sm">暂无定向条件</div>
          </div>
        </template>
      </Grid>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { Button } from 'ant-design-vue';
import { Plus, EmptyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';

interface PlanConfigItem {
  id: string;
  key: string;
  value: string;
}

interface PlanConfig {
  promotion_content?: Record<string, string>;
  placement_rules?: Record<string, string>;
  audience_criteria?: Record<string, string>;
}

interface Props {
  modelValue?: PlanConfig;
}

interface Emits {
  (e: 'update:modelValue', value: PlanConfig): void;
  (e: 'change', value: PlanConfig): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const promotionItems = ref<PlanConfigItem[]>([]);
const placementItems = ref<PlanConfigItem[]>([]);
const audienceItems = ref<PlanConfigItem[]>([]);

// 表格配置
const createGridOptions = (): VxeGridProps<PlanConfigItem> => ({
  columns: [
    {
      field: 'key',
      title: '配置项',
      width: '40%',
      minWidth: 120,
      editRender: { name: 'input' },
    },
    {
      field: 'value',
      title: '配置值',
      width: '45%',
      minWidth: 180,
      editRender: { name: 'input' },
    },
    {
      title: '操作',
      width: '15%',
      minWidth: 80,
      slots: { default: 'action' },
      align: 'center',
    },
  ],
  data: [],
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: { enabled: false },
  toolbarConfig: { enabled: false },
  showOverflow: false,
  border: true,
  autoResize: true,
  scrollY: { enabled: false },
  scrollX: { enabled: false },
});

// 表格事件
const createGridEvents = (): VxeGridListeners<PlanConfigItem> => ({
  editClosed: () => {
    handleChange();
  },
});

// 创建VXE表格实例
const [Grid, promotionGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

const [, placementGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

const [, audienceGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
  gridEvents: createGridEvents(),
});

// 初始化数据
function initializeItems() {
  const config = props.modelValue || {};
  console.log('🔄 PlanConfigEditor 初始化数据:', config);

  // 初始化投放内容
  promotionItems.value = Object.entries(config.promotion_content || {}).map(([key, value], index) => ({
    id: `promotion_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));

  // 初始化投放规则
  placementItems.value = Object.entries(config.placement_rules || {}).map(([key, value], index) => ({
    id: `placement_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));

  // 初始化用户定向条件
  audienceItems.value = Object.entries(config.audience_criteria || {}).map(([key, value], index) => ({
    id: `audience_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));

  // 更新表格数据
  nextTick(() => {
    promotionGridApi.setGridOptions({ data: promotionItems.value });
    placementGridApi.setGridOptions({ data: placementItems.value });
    audienceGridApi.setGridOptions({ data: audienceItems.value });
  });
}

// 添加投放内容项
function addPromotionItem() {
  const newItem = {
    id: `promotion_${Date.now()}_${promotionItems.value.length}`,
    key: '',
    value: '',
  };
  promotionItems.value.push(newItem);
  promotionGridApi.setGridOptions({ data: promotionItems.value });
  handleChange();
}

// 删除投放内容项
function removePromotionItem(index: number) {
  if (index >= 0 && index < promotionItems.value.length) {
    promotionItems.value.splice(index, 1);
    promotionGridApi.setGridOptions({ data: promotionItems.value });
    handleChange();
  }
}

// 添加投放规则项
function addPlacementItem() {
  const newItem = {
    id: `placement_${Date.now()}_${placementItems.value.length}`,
    key: '',
    value: '',
  };
  placementItems.value.push(newItem);
  placementGridApi.setGridOptions({ data: placementItems.value });
  handleChange();
}

// 删除投放规则项
function removePlacementItem(index: number) {
  if (index >= 0 && index < placementItems.value.length) {
    placementItems.value.splice(index, 1);
    placementGridApi.setGridOptions({ data: placementItems.value });
    handleChange();
  }
}

// 添加用户定向条件项
function addAudienceItem() {
  const newItem = {
    id: `audience_${Date.now()}_${audienceItems.value.length}`,
    key: '',
    value: '',
  };
  audienceItems.value.push(newItem);
  audienceGridApi.setGridOptions({ data: audienceItems.value });
  handleChange();
}

// 删除用户定向条件项
function removeAudienceItem(index: number) {
  if (index >= 0 && index < audienceItems.value.length) {
    audienceItems.value.splice(index, 1);
    audienceGridApi.setGridOptions({ data: audienceItems.value });
    handleChange();
  }
}

// 处理变更
function handleChange() {
  try {
    const result: PlanConfig = {};

    // 处理投放内容
    const promotionContent: Record<string, string> = {};
    promotionItems.value.forEach(item => {
      if (item.key && item.key.trim()) {
        promotionContent[item.key.trim()] = item.value || '';
      }
    });
    if (Object.keys(promotionContent).length > 0) {
      result.promotion_content = promotionContent;
    }

    // 处理投放规则
    const placementRules: Record<string, string> = {};
    placementItems.value.forEach(item => {
      if (item.key && item.key.trim()) {
        placementRules[item.key.trim()] = item.value || '';
      }
    });
    if (Object.keys(placementRules).length > 0) {
      result.placement_rules = placementRules;
    }

    // 处理用户定向条件
    const audienceCriteria: Record<string, string> = {};
    audienceItems.value.forEach(item => {
      if (item.key && item.key.trim()) {
        audienceCriteria[item.key.trim()] = item.value || '';
      }
    });
    if (Object.keys(audienceCriteria).length > 0) {
      result.audience_criteria = audienceCriteria;
    }

    console.log('🔄 PlanConfigEditor 数据变更:', result);
    emits('update:modelValue', result);
    emits('change', result);
  } catch (error) {
    console.error('❌ PlanConfigEditor 处理变更失败:', error);
  }
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    initializeItems();
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的key
    const allKeys = [
      ...promotionItems.value.map(item => `promotion_${item.key?.trim()}`),
      ...placementItems.value.map(item => `placement_${item.key?.trim()}`),
      ...audienceItems.value.map(item => `audience_${item.key?.trim()}`),
    ].filter(key => key && key !== 'promotion_' && key !== 'placement_' && key !== 'audience_');
    
    const uniqueKeys = new Set(allKeys);
    if (allKeys.length !== uniqueKeys.size) {
      return {
        valid: false,
        message: '配置项名称不能重复',
      };
    }
    
    return { valid: true };
  },
});
</script>

<style scoped>
.plan-config-editor {
  @apply w-full;
}

.config-section {
  @apply border border-border rounded-lg p-4;
}

.config-section h4 {
  @apply text-foreground;
}

/* 确保删除按钮样式与系统一致 */
.plan-config-editor :deep(.ant-btn-link) {
  color: hsl(var(--destructive));
  padding: 0;
  height: auto;
  line-height: 1.5;
}

.plan-config-editor :deep(.ant-btn-link:hover) {
  color: hsl(var(--destructive) / 80%);
}

/* VXE表格布局优化 */
.plan-config-editor :deep(.vxe-table) {
  width: 100% !important;
  height: auto !important;
  table-layout: fixed;
}

.plan-config-editor :deep(.vxe-table--main-wrapper) {
  overflow: visible !important;
  height: auto !important;
}

.plan-config-editor :deep(.vxe-table--body-wrapper) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

.plan-config-editor :deep(.vxe-table--header-wrapper) {
  overflow: visible !important;
}

/* 确保表格容器高度自适应 */
.plan-config-editor :deep(.vxe-grid) {
  height: auto !important;
}

.plan-config-editor :deep(.vxe-grid--wrapper) {
  height: auto !important;
}

/* 单元格内容优化 */
.plan-config-editor :deep(.vxe-cell) {
  padding: 8px 12px;
  word-wrap: break-word;
  word-break: break-all;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input) {
  width: 100%;
  border: none;
  box-shadow: none;
  background: transparent;
}

.plan-config-editor :deep(.vxe-cell--edit .ant-input:focus) {
  border: 1px solid hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 20%);
  background: hsl(var(--background));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 6px 8px;
    font-size: 14px;
  }

  .plan-config-editor :deep(.ant-btn-link) {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .plan-config-editor :deep(.vxe-cell) {
    padding: 4px 6px;
    font-size: 13px;
  }
}
</style>
