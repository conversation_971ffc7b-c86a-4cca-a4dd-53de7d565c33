<template>
  <div class="app-info-editor">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-medium text-foreground">{{ $t('product.appInfo') }}</h4>
      <Button type="primary" size="small" @click="addItem">
        <Plus class="w-4 h-4 mr-1" />
        添加应用信息
      </Button>
    </div>

    <!-- VXE表格 -->
    <Grid>
      <template #action="{ row, rowIndex }">
        <Button
          type="link"
          danger
          size="small"
          @click="removeItem(rowIndex)"
        >
          删除
        </Button>
      </template>
      <!-- 自定义空状态 -->
      <template #empty>
        <div class="flex-col-center text-muted-foreground min-h-[120px] w-full">
          <EmptyIcon class="size-10 mb-2" />
          <div class="text-sm">{{ $t('common.noData') }}</div>
        </div>
      </template>
    </Grid>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Button } from 'ant-design-vue';
import { Plus, EmptyIcon } from '@vben/icons';
import { $t } from '#/locales';
import type { ProductApi } from '#/api/product';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps, VxeGridListeners } from '#/adapter/vxe-table';

interface Props {
  modelValue?: ProductApi.AppInfo;
}

interface Emits {
  (e: 'update:modelValue', value: ProductApi.AppInfo): void;
  (e: 'change', value: ProductApi.AppInfo): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const items = ref<ProductApi.AppInfoItem[]>([]);

// VXE表格配置
const gridOptions: VxeGridProps<ProductApi.AppInfoItem> = {
  columns: [
    {
      field: 'key',
      title: '标题',
      width: 200,
      editRender: { name: 'input' },
    },
    {
      field: 'value',
      title: '内容',
      minWidth: 200,
      editRender: { name: 'input' },
    },
    {
      title: '操作',
      width: 100,
      slots: { default: 'action' },
      align: 'center',
    },
  ],
  data: [],
  editConfig: {
    mode: 'cell',
    trigger: 'click',
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
  showOverflow: true,
  border: true,
};

// 表格事件
const gridEvents: VxeGridListeners<ProductApi.AppInfoItem> = {
  editClosed: () => {
    handleChange();
  },
};

// 创建VXE表格实例
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

// 初始化数据
function initializeItems() {
  const appInfo = props.modelValue || {};
  console.log('🔄 AppInfoEditor 初始化数据:', appInfo);

  items.value = Object.entries(appInfo).map(([key, value], index) => ({
    id: `app_info_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));

  console.log('📋 AppInfoEditor 转换后的items:', items.value);

  // 更新表格数据
  gridApi.setGridOptions({
    data: items.value,
  });
}

// 添加新项
function addItem() {
  try {
    const newItem = {
      id: `app_info_${Date.now()}_${items.value.length}`,
      key: '',
      value: '',
    };
    items.value.push(newItem);
    console.log('➕ AppInfoEditor 添加新项:', newItem);

    // 更新表格数据
    gridApi.setGridOptions({
      data: items.value,
    });
    handleChange();
  } catch (error) {
    console.error('❌ AppInfoEditor 添加项失败:', error);
  }
}

// 删除项
function removeItem(index: number) {
  try {
    if (index >= 0 && index < items.value.length) {
      const removedItem = items.value.splice(index, 1)[0];
      console.log('🗑️ AppInfoEditor 删除项目:', removedItem);

      // 更新表格数据
      gridApi.setGridOptions({
        data: items.value,
      });
      handleChange();
    }
  } catch (error) {
    console.error('❌ AppInfoEditor 删除项失败:', error);
  }
}

// 处理单元格编辑完成事件
function handleCellEditClosed() {
  handleChange();
}



// 处理变更
function handleChange() {
  try {
    const result: ProductApi.AppInfo = {};

    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        result[item.key.trim()] = item.value || '';
      }
    });

    console.log('🔄 AppInfoEditor 数据变更:', result);
    emits('update:modelValue', result);
    emits('change', result);
  } catch (error) {
    console.error('❌ AppInfoEditor 处理变更失败:', error);
  }
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 避免循环更新
    const currentValue: ProductApi.AppInfo = {};
    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        currentValue[item.key.trim()] = item.value || '';
      }
    });
    
    const newValueStr = JSON.stringify(newValue || {});
    const currentValueStr = JSON.stringify(currentValue);
    
    if (newValueStr !== currentValueStr) {
      initializeItems();
    }
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    // 检查是否有重复的key
    const keys = items.value
      .map(item => item.key?.trim())
      .filter(key => key);
    
    const uniqueKeys = new Set(keys);
    if (keys.length !== uniqueKeys.size) {
      return {
        valid: false,
        message: $t('product.duplicateAppInfoKey'),
      };
    }
    
    return { valid: true };
  },
  getItems: () => items.value,
});
</script>

<style scoped>
.app-info-editor {
  @apply w-full;
}

/* 确保删除按钮样式与系统一致 */
.app-info-editor :deep(.ant-btn-link) {
  color: hsl(var(--destructive));
  padding: 0;
  height: auto;
  line-height: 1.5;
}

.app-info-editor :deep(.ant-btn-link:hover) {
  color: hsl(var(--destructive) / 80%);
}
</style>
