<template>
  <div class="flex items-center space-x-2">
    <img
      v-if="product?.logo"
      :src="product.logo"
      :alt="product.name"
      class="w-8 h-8 rounded object-cover"
    />
    <div v-else class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
      <span class="text-xs text-gray-500">无</span>
    </div>
    <span class="text-sm">{{ product?.name || '-' }}</span>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  product?: {
    id: number;
    name: string;
    logo: string;
  };
}

defineProps<Props>();
</script>

<style scoped>
/* 产品单元格样式 */
</style>
