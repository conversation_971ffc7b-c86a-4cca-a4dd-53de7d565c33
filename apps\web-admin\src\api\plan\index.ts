import { requestClient } from '#/api/request';
import { transformPageParams, transformPageResponse } from '#/api/adapter';
import type { PageFetchParams } from '#/api/request';

export namespace PlanApi {
  /** 投放内容接口 */
  export interface PromotionContent {
    title: string;
    content: string;
  }

  /** 投放规则接口 */
  export interface PlacementRule {
    title: string;
    content: string;
  }

  /** 用户定向条件接口 */
  export interface AudienceCriteria {
    title: string;
    content: string;
  }

  /** 产品信息接口 */
  export interface ProductInfo {
    id: number;
    name: string;
    logo: string;
  }

  /** 计划详情接口 - 前端格式 */
  export interface Plan {
    [key: string]: any;
    id: string;
    planCode: string;
    planName: string;
    productId: string;
    product?: ProductInfo;
    description?: string;
    mainImage?: string;
    status: 0 | 1; // 0-未激活, 1-激活
    promotionContent?: PromotionContent[];
    placementRules?: PlacementRule[];
    audienceCriteria?: AudienceCriteria[];
    createTime?: string;
    updateTime?: string;
  }

  /** 后端API数据结构 */
  export interface BackendPlan {
    id: number;
    plan_code: string;
    name: string;
    product_id: number;
    product?: ProductInfo;
    description: string;
    main_image: string;
    status: string; // "激活" | "未激活"
    promotion_content?: PromotionContent[];
    placement_rules?: PlacementRule[];
    audience_criteria?: AudienceCriteria[];
    create_time: string;
    update_time?: string;
  }

  /** 创建/更新计划参数 - 后端格式 */
  export interface PlanSubmitData {
    product_id: number;
    name: string;
    description: string;
    main_image: string;
    status?: string;
    promotion_content?: PromotionContent[];
    placement_rules?: PlacementRule[];
    audience_criteria?: AudienceCriteria[];
  }

  /** 计划列表查询参数 */
  export interface PlanListParams extends PageFetchParams {
    name?: string;
    product_id?: number;
    status?: string;
    create_time?: string;
  }
}

/**
 * 将后端数据转换为前端格式
 */
export function transformBackendToFrontend(backendData: PlanApi.BackendPlan): PlanApi.Plan {
  return {
    id: backendData.id?.toString() || '',
    planCode: backendData.plan_code || '',
    planName: backendData.name || '',
    productId: backendData.product_id?.toString() || '',
    product: backendData.product,
    description: backendData.description || '',
    mainImage: backendData.main_image || '',
    status: backendData.status === '激活' ? 1 : 0,
    promotionContent: backendData.promotion_content || [],
    placementRules: backendData.placement_rules || [],
    audienceCriteria: backendData.audience_criteria || [],
    createTime: backendData.create_time || '',
    updateTime: backendData.update_time || '',
  };
}

/**
 * 将前端数据转换为后端格式
 */
export function transformFrontendToBackend(frontendData: PlanApi.Plan): PlanApi.PlanSubmitData {
  return {
    product_id: Number(frontendData.productId) || 0,
    name: frontendData.planName || '',
    description: frontendData.description || '',
    main_image: frontendData.mainImage || '',
    status: frontendData.status === 1 ? '激活' : '未激活',
    promotion_content: frontendData.promotionContent || [],
    placement_rules: frontendData.placementRules || [],
    audience_criteria: frontendData.audienceCriteria || [],
  };
}

/**
 * 获取计划列表数据
 */
export async function getPlanList(params: PlanApi.PlanListParams = {}) {
  try {
    console.log('🔄 调用计划列表API，参数:', params);

    // 转换分页参数
    const transformedParams = transformPageParams(params);
    console.log('📤 转换后的参数:', transformedParams);

    // 调用API
    const response = await requestClient.get('/plans', {
      params: transformedParams,
    });

    console.log('📥 API原始响应:', response);

    // 转换响应格式
    const transformedResponse = transformPageResponse(response);
    console.log('🔄 转换后的响应:', transformedResponse);

    // 使用转换函数处理数据
    if (transformedResponse.items && Array.isArray(transformedResponse.items)) {
      transformedResponse.items = transformedResponse.items.map((item: any) =>
        transformBackendToFrontend(item as PlanApi.BackendPlan)
      );
    }

    console.log('✅ 最终返回数据:', transformedResponse);
    return transformedResponse;
  } catch (error) {
    console.error('❌ 获取计划列表失败:', error);
    throw error;
  }
}

/**
 * 获取计划详情
 * @param id 计划 ID
 */
export async function getPlanDetail(id: string) {
  try {
    console.log('🔄 获取计划详情，ID:', id);

    const response = await requestClient.get(`/plans/${id}`);
    console.log('📥 计划详情原始响应:', response);

    // 转换为前端格式
    const transformedData = transformBackendToFrontend(response as PlanApi.BackendPlan);
    console.log('✅ 转换后的计划详情:', transformedData);

    return transformedData;
  } catch (error) {
    console.error('❌ 获取计划详情失败:', error);
    throw error;
  }
}

/**
 * 创建计划
 * @param data 计划数据（前端格式）
 */
export async function createPlan(
  data: Omit<PlanApi.Plan, 'createTime' | 'id' | 'updateTime' | 'planCode'>,
) {
  try {
    console.log('🔄 创建计划，前端数据:', data);

    // 转换为后端格式
    const backendData = transformFrontendToBackend(data as PlanApi.Plan);
    console.log('📤 转换后的后端数据:', backendData);

    const result = await requestClient.post('/plans', backendData);
    console.log('✅ 创建计划成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 创建计划失败:', error);
    throw error;
  }
}

/**
 * 更新计划
 * @param id 计划 ID
 * @param data 计划数据（前端格式）
 */
export async function updatePlan(
  id: string,
  data: Omit<PlanApi.Plan, 'createTime' | 'id' | 'updateTime' | 'planCode'>,
) {
  try {
    console.log('🔄 更新计划，前端数据:', data);

    // 转换为后端格式
    const backendData = transformFrontendToBackend(data as PlanApi.Plan);
    console.log('📤 转换后的后端数据:', backendData);

    const result = await requestClient.put(`/plans/${id}`, backendData);
    console.log('✅ 更新计划成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 更新计划失败:', error);
    throw error;
  }
}

/**
 * 删除计划
 * @param id 计划 ID
 */
export async function deletePlan(id: string) {
  try {
    console.log('🔄 删除计划，ID:', id);

    const result = await requestClient.delete(`/plans/${id}`);
    console.log('✅ 删除计划成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 删除计划失败:', error);
    throw error;
  }
}

/**
 * 修改计划状态
 * @param id 计划 ID
 * @param status 新状态
 */
export async function updatePlanStatus(id: string, status: string) {
  try {
    console.log('🔄 修改计划状态，ID:', id, '状态:', status);

    const result = await requestClient.put(`/plans/${id}/status`, { status });
    console.log('✅ 修改计划状态成功:', result);

    return result;
  } catch (error) {
    console.error('❌ 修改计划状态失败:', error);
    throw error;
  }
}
