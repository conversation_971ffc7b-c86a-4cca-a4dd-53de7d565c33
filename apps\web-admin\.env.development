# 端口号
VITE_PORT=5777

VITE_BASE=/

# 接口地址
# Mock模式：使用 /api
# 真实后台：使用 http://localhost:8080/api 或您的后台地址


VITE_GLOB_API_URL=/api
VITE_NITRO_MOCK=true



# 是否开启 Nitro Mock服务，true 为开启，false 为关闭
# 开发调试时保持 true，对接真实后台时改为 false


# VITE_GLOB_API_URL=https://app.fbads.bywst.top/mng/
# VITE_NITRO_MOCK=false

# VITE_NITRO_MOCK=false

# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true
