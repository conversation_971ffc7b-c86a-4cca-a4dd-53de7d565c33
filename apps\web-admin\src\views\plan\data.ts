import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { PlanApi } from '#/api';

import { requestClient } from '#/api/request';
import { $t } from '#/locales';

// 上传函数
async function uploadFile({
  file,
  onError,
  onProgress,
  onSuccess,
}: {
  file: File;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: any, file: File) => void;
}) {
  try {
    onProgress?.({ percent: 0 });
    const data = await requestClient.upload('/upload', { file });
    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ProductSelector',
      componentProps: {
        placeholder: '请选择产品',
        allowClear: true,
      },
      fieldName: 'productId',
      label: $t('plan.product'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'planName',
      label: $t('plan.planName'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: $t('plan.description'),
      rules: 'required',
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        customRequest: uploadFile,
        listType: 'picture-card',
        maxCount: 1,
        multiple: false,
        showUploadList: true,
      },
      fieldName: 'mainImage',
      label: $t('plan.mainImage'),
      help: '支持jpg、png格式，建议尺寸400x300像素',
      rules: 'required',
      renderComponentContent: () => {
        return {
          default: () => '上传主图',
        };
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('plan.status.active'), value: 1 },
          { label: $t('plan.status.inactive'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('plan.status.label'),
    },
    {
      component: 'PlanConfigEditor',
      componentProps: {},
      fieldName: 'configData',
      label: '计划配置',
      help: '配置投放内容、投放规则和用户定向条件',
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: $t('plan.searchPlaceholder'),
      },
      fieldName: 'name',
      label: $t('plan.planName'),
    },
    {
      component: 'ProductSelector',
      componentProps: {
        allowClear: true,
        placeholder: '请选择产品',
      },
      fieldName: 'product_id',
      label: $t('plan.product'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: $t('plan.statusPlaceholder'),
        options: [
          { label: $t('plan.status.active'), value: '激活' },
          { label: $t('plan.status.inactive'), value: '未激活' },
        ],
      },
      fieldName: 'status',
      label: $t('plan.status.label'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择创建时间',
        format: 'YYYY-MM-DD',
      },
      fieldName: 'create_time',
      label: $t('plan.createTime'),
    },
  ];
}

export function useColumns<T = PlanApi.Plan>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'planCode',
      title: $t('plan.planCode'),
      width: 140,
    },
    {
      field: 'planName',
      title: $t('plan.planName'),
      minWidth: 150,
    },
    {
      field: 'product',
      title: $t('plan.product'),
      width: 200,
      slots: { default: 'product' },
    },
    {
      field: 'mainImage',
      title: $t('plan.mainImage'),
      width: 100,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '48px', height: '48px' },
        },
      },
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('plan.status.label'),
      width: 100,
    },
    {
      field: 'description',
      title: $t('plan.description'),
      minWidth: 200,
      showOverflow: true,
    },
    {
      field: 'createTime',
      title: $t('plan.createTime'),
      width: 180,
      formatter: ({ cellValue }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : '-';
      },
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'planName',
          nameTitle: $t('plan.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('plan.operation'),
      width: 130,
    },
  ];
}
