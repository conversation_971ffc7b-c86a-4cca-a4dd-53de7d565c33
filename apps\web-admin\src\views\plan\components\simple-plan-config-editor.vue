<template>
  <div class="simple-plan-config-editor">
    <!-- 投放内容配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放内容</h4>
        <Button type="primary" size="small" @click="addPromotionItem">
          <Plus class="w-4 h-4 mr-1" />
          添加投放内容
        </Button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(item, index) in promotionItems" 
          :key="item.id"
          class="flex items-center space-x-3 p-3 border border-border rounded-lg"
        >
          <div class="flex-1">
            <Input
              v-model:value="item.title"
              placeholder="标题"
              class="mb-2"
              @change="handleChange"
            />
            <Input
              v-model:value="item.content"
              placeholder="内容"
              @change="handleChange"
            />
          </div>
          <Button
            type="link"
            danger
            size="small"
            @click="removePromotionItem(index)"
          >
            删除
          </Button>
        </div>
        
        <div v-if="promotionItems.length === 0" class="text-center py-8 text-muted-foreground">
          暂无投放内容配置
        </div>
      </div>
    </div>

    <!-- 投放规则配置 -->
    <div class="config-section mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">投放规则</h4>
        <Button type="primary" size="small" @click="addPlacementItem">
          <Plus class="w-4 h-4 mr-1" />
          添加投放规则
        </Button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(item, index) in placementItems" 
          :key="item.id"
          class="flex items-center space-x-3 p-3 border border-border rounded-lg"
        >
          <div class="flex-1">
            <Input
              v-model:value="item.title"
              placeholder="标题"
              class="mb-2"
              @change="handleChange"
            />
            <Input
              v-model:value="item.content"
              placeholder="内容"
              @change="handleChange"
            />
          </div>
          <Button
            type="link"
            danger
            size="small"
            @click="removePlacementItem(index)"
          >
            删除
          </Button>
        </div>
        
        <div v-if="placementItems.length === 0" class="text-center py-8 text-muted-foreground">
          暂无投放规则配置
        </div>
      </div>
    </div>

    <!-- 用户定向条件配置 -->
    <div class="config-section">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-foreground">用户定向条件</h4>
        <Button type="primary" size="small" @click="addAudienceItem">
          <Plus class="w-4 h-4 mr-1" />
          添加定向条件
        </Button>
      </div>
      
      <div class="space-y-3">
        <div 
          v-for="(item, index) in audienceItems" 
          :key="item.id"
          class="flex items-center space-x-3 p-3 border border-border rounded-lg"
        >
          <div class="flex-1">
            <Input
              v-model:value="item.title"
              placeholder="标题"
              class="mb-2"
              @change="handleChange"
            />
            <Input
              v-model:value="item.content"
              placeholder="内容"
              @change="handleChange"
            />
          </div>
          <Button
            type="link"
            danger
            size="small"
            @click="removeAudienceItem(index)"
          >
            删除
          </Button>
        </div>
        
        <div v-if="audienceItems.length === 0" class="text-center py-8 text-muted-foreground">
          暂无定向条件配置
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Button, Input } from 'ant-design-vue';
import { Plus } from '@vben/icons';

interface ConfigItem {
  id: string;
  title: string;
  content: string;
}

interface BackendConfigItem {
  title: string;
  content: string;
}

interface Props {
  modelValue?: any;
}

interface Emits {
  (e: 'update:modelValue', value: any): void;
  (e: 'change', value: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const promotionItems = ref<ConfigItem[]>([]);
const placementItems = ref<ConfigItem[]>([]);
const audienceItems = ref<ConfigItem[]>([]);

// 初始化数据
function initializeItems() {
  const config = props.modelValue || {};
  console.log('🔄 SimplePlanConfigEditor 初始化数据:', config);

  // 初始化投放内容
  promotionItems.value = (config.promotion_content || []).map((item: BackendConfigItem, index: number) => ({
    id: `promotion_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化投放规则
  placementItems.value = (config.placement_rules || []).map((item: BackendConfigItem, index: number) => ({
    id: `placement_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  // 初始化用户定向条件
  audienceItems.value = (config.audience_criteria || []).map((item: BackendConfigItem, index: number) => ({
    id: `audience_${Date.now()}_${index}`,
    title: item.title || '',
    content: item.content || '',
  }));

  console.log('📋 SimplePlanConfigEditor 初始化完成:', {
    promotion: promotionItems.value.length,
    placement: placementItems.value.length,
    audience: audienceItems.value.length,
  });
}

// 添加投放内容项
function addPromotionItem() {
  console.log('🔄 添加投放内容项');
  const newItem = {
    id: `promotion_${Date.now()}_${promotionItems.value.length}`,
    title: '',
    content: '',
  };
  promotionItems.value.push(newItem);
  handleChange();
}

// 删除投放内容项
function removePromotionItem(index: number) {
  console.log('🗑️ 删除投放内容项:', index);
  promotionItems.value.splice(index, 1);
  handleChange();
}

// 添加投放规则项
function addPlacementItem() {
  console.log('🔄 添加投放规则项');
  const newItem = {
    id: `placement_${Date.now()}_${placementItems.value.length}`,
    title: '',
    content: '',
  };
  placementItems.value.push(newItem);
  handleChange();
}

// 删除投放规则项
function removePlacementItem(index: number) {
  console.log('🗑️ 删除投放规则项:', index);
  placementItems.value.splice(index, 1);
  handleChange();
}

// 添加用户定向条件项
function addAudienceItem() {
  console.log('🔄 添加用户定向条件项');
  const newItem = {
    id: `audience_${Date.now()}_${audienceItems.value.length}`,
    title: '',
    content: '',
  };
  audienceItems.value.push(newItem);
  handleChange();
}

// 删除用户定向条件项
function removeAudienceItem(index: number) {
  console.log('🗑️ 删除用户定向条件项:', index);
  audienceItems.value.splice(index, 1);
  handleChange();
}

// 处理变更
function handleChange() {
  const result = { ...props.modelValue };

  // 处理投放内容
  result.promotion_content = promotionItems.value
    .filter(item => item.title && item.title.trim())
    .map(item => ({
      title: item.title.trim(),
      content: item.content || '',
    }));

  // 处理投放规则
  result.placement_rules = placementItems.value
    .filter(item => item.title && item.title.trim())
    .map(item => ({
      title: item.title.trim(),
      content: item.content || '',
    }));

  // 处理用户定向条件
  result.audience_criteria = audienceItems.value
    .filter(item => item.title && item.title.trim())
    .map(item => ({
      title: item.title.trim(),
      content: item.content || '',
    }));

  console.log('🔄 SimplePlanConfigEditor 数据变更:', result);
  emits('update:modelValue', result);
  emits('change', result);
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  () => {
    initializeItems();
  },
  { immediate: true, deep: true }
);
</script>

<style scoped>
.simple-plan-config-editor {
  @apply w-full;
}

.config-section {
  @apply border border-border rounded-lg p-4;
}

.config-section h4 {
  @apply text-foreground;
}
</style>
