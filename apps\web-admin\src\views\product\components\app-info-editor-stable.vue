<template>
  <div class="app-info-editor-stable">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-sm font-medium">应用信息</h4>
      <Button type="primary" size="small" @click="addItem">
        <Plus class="w-4 h-4 mr-1" />
        添加应用信息
      </Button>
    </div>
    
    <div v-if="items.length === 0" class="empty-state">
      <!-- 空白提示，不显示文字 -->
    </div>

    <div v-else class="app-info-table">
      <div class="table-header">
        <div class="header-cell title-col">标题</div>
        <div class="header-cell content-col">内容</div>
        <div class="header-cell action-col">操作</div>
      </div>

      <div
        v-for="(item, index) in items"
        :key="item.id"
        class="table-row"
      >
        <div class="table-cell title-col">
          <Input
            v-model:value="item.key"
            placeholder="请输入标题"
            size="small"
            class="table-input"
            :bordered="false"
            @blur="updateData"
          />
        </div>
        <div class="table-cell content-col">
          <Input
            v-model:value="item.value"
            placeholder="请输入内容"
            size="small"
            class="table-input"
            :bordered="false"
            @blur="updateData"
          />
        </div>
        <div class="table-cell action-col">
          <Button
            type="link"
            danger
            size="small"
            @click="removeItem(index)"
          >
            删除
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Button, Input } from 'ant-design-vue';
import { Plus } from '@vben/icons';

interface AppInfoItem {
  id: string;
  key: string;
  value: string;
}

interface Props {
  modelValue?: Record<string, string>;
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, string>): void;
  (e: 'change', value: Record<string, string>): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
});

const emits = defineEmits<Emits>();

// 内部状态
const items = ref<AppInfoItem[]>([]);

// 初始化数据
function initializeItems() {
  const appInfo = props.modelValue || {};
  console.log('🔄 AppInfoEditor 初始化数据:', appInfo);
  
  items.value = Object.entries(appInfo).map(([key, value], index) => ({
    id: `app_info_${Date.now()}_${index}`,
    key,
    value: value || '',
  }));
  
  console.log('📋 AppInfoEditor 转换后的items:', items.value);
}

// 添加新项
function addItem() {
  const newItem = {
    id: `app_info_${Date.now()}_${items.value.length}`,
    key: '',
    value: '',
  };
  items.value.push(newItem);
  console.log('➕ AppInfoEditor 添加新项:', newItem);
}

// 删除项
function removeItem(index: number) {
  if (index >= 0 && index < items.value.length) {
    const removedItem = items.value.splice(index, 1)[0];
    console.log('🗑️ AppInfoEditor 删除项:', removedItem);
    updateData();
  }
}

// 更新数据
function updateData() {
  const result: Record<string, string> = {};
  
  items.value.forEach(item => {
    if (item.key && item.key.trim()) {
      result[item.key.trim()] = item.value || '';
    }
  });
  
  console.log('🔄 AppInfoEditor 数据更新:', result);
  emits('update:modelValue', result);
  emits('change', result);
}

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    const currentValue: Record<string, string> = {};
    items.value.forEach(item => {
      if (item.key && item.key.trim()) {
        currentValue[item.key.trim()] = item.value || '';
      }
    });
    
    const newValueStr = JSON.stringify(newValue || {});
    const currentValueStr = JSON.stringify(currentValue);
    
    if (newValueStr !== currentValueStr) {
      initializeItems();
    }
  },
  { immediate: true, deep: true }
);

// 暴露方法给父组件
defineExpose({
  validate: () => {
    const keys = items.value
      .map(item => item.key?.trim())
      .filter(key => key);
    
    const uniqueKeys = new Set(keys);
    if (keys.length !== uniqueKeys.size) {
      return {
        valid: false,
        message: '应用信息标题不能重复',
      };
    }
    
    return { valid: true };
  },
  getItems: () => items.value,
});
</script>

<style scoped>
.app-info-editor-stable {
  @apply w-full;
}

.empty-state {
  height: 120px;
  border: 1px dashed var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--component-background);
}

.app-info-table {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--component-background);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 5fr 5fr 2fr;
  background-color: var(--background-color-light);
  border-bottom: 1px solid var(--border-color);
}

.table-row {
  display: grid;
  grid-template-columns: 5fr 5fr 2fr;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: var(--background-color-light);
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell {
  padding: 12px;
  font-weight: 500;
  font-size: 14px;
  color: var(--text-color);
  border-right: 1px solid var(--border-color);
}

.header-cell:last-child {
  border-right: none;
}

.table-cell {
  padding: 0;
  border-right: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  min-height: 40px;
}

.table-cell:last-child {
  border-right: none;
  padding: 8px;
  justify-content: center;
}

.title-col {
  position: relative;
}

.title-col::before {
  content: '';
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: var(--primary-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.title-col:hover::before {
  opacity: 0.6;
}

.content-col {
  position: relative;
}

.content-col::before {
  content: '';
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: var(--primary-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.content-col:hover::before {
  opacity: 0.6;
}

.action-col {
  justify-content: center;
  text-align: center;
}

/* 表格输入框样式 - 让输入框看起来像表格单元格内容 */
.table-input :deep(.ant-input) {
  width: 100%;
  height: 40px;
  padding: 8px 12px 8px 16px;
  border: none !important;
  background-color: transparent !important;
  color: var(--text-color);
  box-shadow: none !important;
  outline: none !important;
  border-radius: 0;
  cursor: text;
}

.table-input :deep(.ant-input:hover) {
  border: none !important;
  background-color: var(--background-color-light) !important;
}

.table-input :deep(.ant-input:focus) {
  border: none !important;
  background-color: var(--background-color-light) !important;
  box-shadow: inset 0 0 0 2px var(--primary-color) !important;
}

.table-input :deep(.ant-input::placeholder) {
  color: var(--text-color-help);
  font-style: italic;
}

/* 删除按钮样式 */
.table-cell :deep(.ant-btn-link) {
  color: var(--error-color);
  padding: 0;
}

.table-cell :deep(.ant-btn-link:hover) {
  color: var(--error-color-hover);
}
</style>
